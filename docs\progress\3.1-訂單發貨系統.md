# 5-訂單發貨功能整合

**方案編號**: 5
**開始日期**: 2025-01-27
**完成日期**: 進行中
**狀態**: 🚧 進行中
**負責人**: AI Assistant
**上級方案**: [0-總方案](./0-總方案.md)
**前置依賴**: [3-訂單系統](./3-訂單系統.md), [4-用戶認證系統](./4-用戶認證系統.md)

## 方案概述

將發貨功能整合到訂單系統中，使發貨成為訂單管理的核心功能而非獨立業務模塊。整合現有的ShippingService到OrderService中，提供統一的訂單+發貨管理體驗。

## 方案修正說明

**原方案問題**: 將發貨功能設計為獨立的物流系統
**修正方案**: 發貨功能應該是訂單系統的組成部分
**核心思路**: 將ShippingService的功能整合到OrderService中，保持業務邏輯的內聚性

## 技術方案

### 後端架構
- **Spring Boot**: 發貨API服務
- **MyBatis**: 發貨數據持久化
- **MySQL**: 發貨信息存儲
- **JWT**: 權限控制（管理員發貨權限）

### 前端架構
- **Next.js**: 用戶端發貨進度查看
- **Ant Design Pro**: 管理端發貨操作界面
- **Zustand**: 發貨狀態管理
- **TypeScript**: 類型安全

## 實施計劃

### 階段1：後端OrderService整合
- [ ] 擴展OrderService接口，添加發貨相關方法
- [ ] 將ShippingService核心邏輯遷移到OrderServiceImpl
- [ ] 擴展OrderController，添加發貨相關端點
- [ ] 保持現有數據庫表結構和關聯關係

### 階段2：前端OrderStore整合
- [ ] 擴展OrderStore，添加發貨相關狀態和方法
- [ ] 更新訂單詳情頁面，集成發貨信息展示
- [ ] 更新訂單管理頁面，添加發貨操作功能
- [ ] 實現發貨狀態篩選和管理功能

### 階段3：權限和安全控制
- [ ] 實現發貨操作的ADMIN權限控制
- [ ] 添加發貨操作的審計日誌
- [ ] 確保用戶只能查看自己訂單的發貨信息
- [ ] 實現發貨狀態的業務邏輯驗證

### 階段4：系統集成測試
- [ ] 測試訂單+發貨的完整業務流程
- [ ] 驗證權限控制的有效性
- [ ] 測試前後端數據同步
- [ ] 用戶體驗和界面集成測試

## 實現詳情

### 1. 後端OrderService整合

#### OrderService接口擴展
```java
public interface OrderService {
    // 現有方法...

    // 新增發貨相關方法
    ShippingInfo shipOrder(Long orderId, ShippingRequest request);
    ShippingInfo getOrderShipping(Long orderId);
    ShippingInfo updateOrderShipping(Long orderId, ShippingRequest request);
    List<Order> getOrdersReadyToShip();
}
```

#### OrderController發貨端點
- `POST /api/orders/{id}/ship` - 發貨操作（整合到訂單API）
- `GET /api/orders/{id}/shipping` - 獲取訂單發貨信息
- `PUT /api/orders/{id}/shipping` - 更新訂單發貨信息
- `GET /api/orders/ready-to-ship` - 獲取待發貨訂單列表

### 2. 前端OrderStore整合

#### OrderStore狀態擴展
```typescript
interface OrderState {
  // 現有狀態...

  // 新增發貨相關方法
  shipOrder: (orderId: number, shippingData: ShippingRequest) => Promise<ShippingInfo>;
  getOrderShipping: (orderId: number) => ShippingInfo | null;
  updateOrderShipping: (orderId: number, shippingData: ShippingRequest) => Promise<ShippingInfo>;
  getOrdersReadyToShip: () => Order[];
}
```

#### 訂單詳情頁面整合
- 在訂單詳情中直接顯示發貨信息
- 發貨狀態時間軸展示
- 物流跟踪信息集成
- 管理員發貨操作按鈕

#### 訂單管理頁面整合
- 發貨狀態篩選器
- 待發貨訂單突出顯示
- 批量發貨操作（管理員）
- 發貨信息快速錄入

### 3. 業務邏輯整合

#### 發貨操作流程（在訂單系統內）
1. 在訂單列表中選擇待發貨訂單
2. 點擊"發貨"按鈕（集成在訂單操作中）
3. 錄入物流信息（快遞公司、運單號）
4. 確認發貨操作
5. 自動更新訂單狀態為"已發貨"
6. 在訂單詳情中顯示發貨信息

## 數據庫設計

### 保持現有表結構
- **shipping_info表**: 已存在，保持現有結構
- **orders表**: 已存在，通過order_id關聯發貨信息
- **order_statuses表**: 已存在，包含SHIPPED等發貨相關狀態

### 現有表關聯關係
```sql
-- 發貨信息表（已存在）
shipping_info.order_id -> orders.id

-- 訂單狀態表（已存在）
orders.status_id -> order_statuses.id
```

### 無需新建表
由於發貨功能整合到訂單系統，現有的數據庫結構已經足夠支持所需功能，無需創建額外的表。

## 業務邏輯整合

### 訂單狀態流轉（包含發貨）
1. **PENDING** (待處理) → **PAID** (已付款)
2. **PAID** (已付款) → **PROCESSING** (處理中)
3. **PROCESSING** (處理中) → **SHIPPED** (已發貨) ← 發貨操作觸發
4. **SHIPPED** (已發貨) → **DELIVERED** (已送達)

### 發貨權限控制（在訂單系統內）
- 只有ADMIN角色可以執行發貨操作
- 只能對PAID或PROCESSING狀態的訂單進行發貨
- 發貨操作直接更新訂單狀態為SHIPPED
- 發貨後可以更新物流信息，但不能撤銷發貨狀態

### 整合優勢
- 發貨成為訂單生命週期的自然組成部分
- 統一的訂單+發貨管理界面
- 簡化的業務邏輯和數據流
- 更好的用戶體驗和管理效率

## 文件結構調整

```
後端 (ecommerce-backend):
├── src/main/java/io/github/roshad/ecommerce/
│   ├── order/
│   │   ├── OrderService.java (擴展發貨方法)
│   │   ├── OrderServiceImpl.java (整合發貨邏輯)
│   │   ├── OrderController.java (添加發貨端點)
│   │   └── Order.java (可選：添加發貨信息關聯)
│   └── shipping/
│       ├── ShippingInfo.java (保持現有)
│       ├── ShippingMapper.java (保持現有)
│       ├── ShippingRequest.java (保持現有)
│       └── ShippingService.java (邏輯遷移到OrderService)

前端用戶端 (mall-client):
├── src/
│   ├── store/
│   │   └── orderStore.ts (擴展發貨功能)
│   ├── types/
│   │   └── order.ts (包含發貨類型定義)
│   ├── components/
│   │   └── Order/
│   │       ├── OrderShipping.tsx (發貨信息組件)
│   │       ├── ShippingTimeline.tsx (發貨時間軸)
│   │       └── ShippingActions.tsx (管理員發貨操作)
│   └── app/orders/
│       ├── page.tsx (整合發貨篩選)
│       └── [id]/page.tsx (整合發貨信息展示)
```

## 當前進度

### 已完成 ✅
- 基礎訂單系統
- 用戶認證系統
- 訂單狀態管理
- 獨立的ShippingService（待整合）

### 進行中 🚧
- OrderService發貨功能整合
- OrderController發貨端點添加
- OrderStore發貨狀態管理
- 訂單頁面發貨功能集成

### 待開始 ⏳
- 發貨權限控制完善
- 發貨操作審計日誌
- 批量發貨功能
- 發貨通知系統

## 整合效果預期

### 用戶體驗改善
- 在訂單詳情中直接查看發貨信息
- 統一的訂單管理界面
- 更直觀的訂單狀態流轉

### 管理效率提升
- 在訂單列表中直接發貨操作
- 減少系統間跳轉
- 統一的訂單+發貨數據視圖

### 技術架構優化
- 減少服務間依賴
- 簡化業務邏輯
- 提高代碼內聚性

---

**最後更新**: 2025-01-27
**下次檢查**: 2025-01-28
