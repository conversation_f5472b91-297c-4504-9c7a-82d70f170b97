{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mall/mall-client/src/store/cartStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\n\n// 購物車商品接口\nexport interface CartItem {\n  id: number;\n  name: string;\n  price: number;\n  imageUrl: string;\n  quantity: number;\n  stock: number;\n}\n\n// 購物車狀態接口\ninterface CartState {\n  items: CartItem[];\n  totalItems: number;\n  totalPrice: number;\n  \n  // 操作方法\n  addItem: (product: Omit<CartItem, 'quantity'>) => void;\n  removeItem: (id: number) => void;\n  updateQuantity: (id: number, quantity: number) => void;\n  clearCart: () => void;\n  getItemQuantity: (id: number) => number;\n}\n\n// 計算總數量\nconst calculateTotalItems = (items: CartItem[]): number => {\n  return items.reduce((total, item) => total + item.quantity, 0);\n};\n\n// 計算總價格\nconst calculateTotalPrice = (items: CartItem[]): number => {\n  return items.reduce((total, item) => total + (item.price * item.quantity), 0);\n};\n\n// 創建購物車 store\nexport const useCartStore = create<CartState>()(\n  persist(\n    (set, get) => ({\n      items: [],\n      totalItems: 0,\n      totalPrice: 0,\n\n      // 添加商品到購物車\n      addItem: (product) => {\n        const { items } = get();\n        const existingItem = items.find(item => item.id === product.id);\n\n        let newItems: CartItem[];\n        \n        if (existingItem) {\n          // 如果商品已存在，增加數量（不超過庫存）\n          const newQuantity = Math.min(existingItem.quantity + 1, product.stock);\n          newItems = items.map(item =>\n            item.id === product.id\n              ? { ...item, quantity: newQuantity }\n              : item\n          );\n        } else {\n          // 如果是新商品，添加到購物車\n          newItems = [...items, { ...product, quantity: 1 }];\n        }\n\n        set({\n          items: newItems,\n          totalItems: calculateTotalItems(newItems),\n          totalPrice: calculateTotalPrice(newItems),\n        });\n      },\n\n      // 從購物車移除商品\n      removeItem: (id) => {\n        const { items } = get();\n        const newItems = items.filter(item => item.id !== id);\n        \n        set({\n          items: newItems,\n          totalItems: calculateTotalItems(newItems),\n          totalPrice: calculateTotalPrice(newItems),\n        });\n      },\n\n      // 更新商品數量\n      updateQuantity: (id, quantity) => {\n        const { items } = get();\n        \n        if (quantity <= 0) {\n          // 如果數量為0或負數，移除商品\n          get().removeItem(id);\n          return;\n        }\n\n        const newItems = items.map(item => {\n          if (item.id === id) {\n            // 確保數量不超過庫存\n            const newQuantity = Math.min(quantity, item.stock);\n            return { ...item, quantity: newQuantity };\n          }\n          return item;\n        });\n\n        set({\n          items: newItems,\n          totalItems: calculateTotalItems(newItems),\n          totalPrice: calculateTotalPrice(newItems),\n        });\n      },\n\n      // 清空購物車\n      clearCart: () => {\n        set({\n          items: [],\n          totalItems: 0,\n          totalPrice: 0,\n        });\n      },\n\n      // 獲取特定商品的數量\n      getItemQuantity: (id) => {\n        const { items } = get();\n        const item = items.find(item => item.id === id);\n        return item ? item.quantity : 0;\n      },\n    }),\n    {\n      name: 'cart-storage', // 本地存儲的key\n      // 可以選擇性地存儲某些字段\n      partialize: (state) => ({\n        items: state.items,\n        totalItems: state.totalItems,\n        totalPrice: state.totalPrice,\n      }),\n    }\n  )\n);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AA0BA,QAAQ;AACR,MAAM,sBAAsB,CAAC;IAC3B,OAAO,MAAM,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,QAAQ,EAAE;AAC9D;AAEA,QAAQ;AACR,MAAM,sBAAsB,CAAC;IAC3B,OAAO,MAAM,MAAM,CAAC,CAAC,OAAO,OAAS,QAAS,KAAK,KAAK,GAAG,KAAK,QAAQ,EAAG;AAC7E;AAGO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,OAAO,EAAE;QACT,YAAY;QACZ,YAAY;QAEZ,WAAW;QACX,SAAS,CAAC;YACR,MAAM,EAAE,KAAK,EAAE,GAAG;YAClB,MAAM,eAAe,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,QAAQ,EAAE;YAE9D,IAAI;YAEJ,IAAI,cAAc;gBAChB,sBAAsB;gBACtB,MAAM,cAAc,KAAK,GAAG,CAAC,aAAa,QAAQ,GAAG,GAAG,QAAQ,KAAK;gBACrE,WAAW,MAAM,GAAG,CAAC,CAAA,OACnB,KAAK,EAAE,KAAK,QAAQ,EAAE,GAClB;wBAAE,GAAG,IAAI;wBAAE,UAAU;oBAAY,IACjC;YAER,OAAO;gBACL,gBAAgB;gBAChB,WAAW;uBAAI;oBAAO;wBAAE,GAAG,OAAO;wBAAE,UAAU;oBAAE;iBAAE;YACpD;YAEA,IAAI;gBACF,OAAO;gBACP,YAAY,oBAAoB;gBAChC,YAAY,oBAAoB;YAClC;QACF;QAEA,WAAW;QACX,YAAY,CAAC;YACX,MAAM,EAAE,KAAK,EAAE,GAAG;YAClB,MAAM,WAAW,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAElD,IAAI;gBACF,OAAO;gBACP,YAAY,oBAAoB;gBAChC,YAAY,oBAAoB;YAClC;QACF;QAEA,SAAS;QACT,gBAAgB,CAAC,IAAI;YACnB,MAAM,EAAE,KAAK,EAAE,GAAG;YAElB,IAAI,YAAY,GAAG;gBACjB,iBAAiB;gBACjB,MAAM,UAAU,CAAC;gBACjB;YACF;YAEA,MAAM,WAAW,MAAM,GAAG,CAAC,CAAA;gBACzB,IAAI,KAAK,EAAE,KAAK,IAAI;oBAClB,YAAY;oBACZ,MAAM,cAAc,KAAK,GAAG,CAAC,UAAU,KAAK,KAAK;oBACjD,OAAO;wBAAE,GAAG,IAAI;wBAAE,UAAU;oBAAY;gBAC1C;gBACA,OAAO;YACT;YAEA,IAAI;gBACF,OAAO;gBACP,YAAY,oBAAoB;gBAChC,YAAY,oBAAoB;YAClC;QACF;QAEA,QAAQ;QACR,WAAW;YACT,IAAI;gBACF,OAAO,EAAE;gBACT,YAAY;gBACZ,YAAY;YACd;QACF;QAEA,YAAY;QACZ,iBAAiB,CAAC;YAChB,MAAM,EAAE,KAAK,EAAE,GAAG;YAClB,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAC5C,OAAO,OAAO,KAAK,QAAQ,GAAG;QAChC;IACF,CAAC,GACD;IACE,MAAM;IACN,eAAe;IACf,YAAY,CAAC,QAAU,CAAC;YACtB,OAAO,MAAM,KAAK;YAClB,YAAY,MAAM,UAAU;YAC5B,YAAY,MAAM,UAAU;QAC9B,CAAC;AACH", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mall/mall-client/src/store/authStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\n\n// 用戶信息接口\nexport interface User {\n  id: number;\n  username: string;\n  email: string;\n  role: string;\n  createdAt: string;\n}\n\n// 認證狀態接口\ninterface AuthState {\n  user: User | null;\n  token: string | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  \n  // 操作方法\n  login: (username: string, password: string) => Promise<{ success: boolean; message: string }>;\n  register: (username: string, password: string, confirmPassword: string, email: string) => Promise<{ success: boolean; message: string }>;\n  logout: () => void;\n  checkAuth: () => Promise<void>;\n  updateUser: (user: User) => void;\n  setLoading: (loading: boolean) => void;\n}\n\n// API 基礎URL\nconst API_BASE_URL = 'http://localhost:8080/api/auth';\n\n// 創建認證 store\nexport const useAuthStore = create<AuthState>()(\n  persist(\n    (set, get) => ({\n      user: null,\n      token: null,\n      isAuthenticated: false,\n      isLoading: false,\n\n      // 登錄\n      login: async (username: string, password: string) => {\n        set({ isLoading: true });\n        \n        try {\n          const response = await fetch(`${API_BASE_URL}/login`, {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json',\n            },\n            body: JSON.stringify({ username, password }),\n          });\n\n          const data = await response.json();\n\n          if (data.success) {\n            set({\n              user: data.user,\n              token: data.token,\n              isAuthenticated: true,\n              isLoading: false,\n            });\n            return { success: true, message: data.message };\n          } else {\n            set({ isLoading: false });\n            return { success: false, message: data.message };\n          }\n        } catch (error) {\n          set({ isLoading: false });\n          return { success: false, message: '登錄失敗，請檢查網絡連接' };\n        }\n      },\n\n      // 註冊\n      register: async (username: string, password: string, confirmPassword: string, email: string) => {\n        set({ isLoading: true });\n        \n        try {\n          const response = await fetch(`${API_BASE_URL}/register`, {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json',\n            },\n            body: JSON.stringify({\n              username,\n              password,\n              confirmPassword,\n              email,\n            }),\n          });\n\n          const data = await response.json();\n\n          if (data.success) {\n            set({ isLoading: false });\n            return { success: true, message: data.message };\n          } else {\n            set({ isLoading: false });\n            return { success: false, message: data.message };\n          }\n        } catch (error) {\n          set({ isLoading: false });\n          return { success: false, message: '註冊失敗，請檢查網絡連接' };\n        }\n      },\n\n      // 登出\n      logout: () => {\n        set({\n          user: null,\n          token: null,\n          isAuthenticated: false,\n          isLoading: false,\n        });\n      },\n\n      // 檢查認證狀態\n      checkAuth: async () => {\n        const { token } = get();\n        \n        if (!token) {\n          return;\n        }\n\n        set({ isLoading: true });\n\n        try {\n          const response = await fetch(`${API_BASE_URL}/profile`, {\n            method: 'GET',\n            headers: {\n              'Authorization': `Bearer ${token}`,\n              'Content-Type': 'application/json',\n            },\n          });\n\n          if (response.ok) {\n            const data = await response.json();\n            if (data.success) {\n              set({\n                user: data.user,\n                isAuthenticated: true,\n                isLoading: false,\n              });\n            } else {\n              // Token 無效，清除認證狀態\n              get().logout();\n            }\n          } else {\n            // Token 無效，清除認證狀態\n            get().logout();\n          }\n        } catch (error) {\n          console.error('檢查認證狀態失敗:', error);\n          get().logout();\n        }\n      },\n\n      // 更新用戶信息\n      updateUser: (user: User) => {\n        set({ user });\n      },\n\n      // 設置加載狀態\n      setLoading: (loading: boolean) => {\n        set({ isLoading: loading });\n      },\n    }),\n    {\n      name: 'auth-storage', // 本地存儲的key\n      // 只持久化必要的字段\n      partialize: (state) => ({\n        user: state.user,\n        token: state.token,\n        isAuthenticated: state.isAuthenticated,\n      }),\n    }\n  )\n);\n\n// 獲取認證頭部\nexport const getAuthHeaders = () => {\n  const token = useAuthStore.getState().token;\n  return token ? { Authorization: `Bearer ${token}` } : {};\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AA2BA,YAAY;AACZ,MAAM,eAAe;AAGd,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,MAAM;QACN,OAAO;QACP,iBAAiB;QACjB,WAAW;QAEX,KAAK;QACL,OAAO,OAAO,UAAkB;YAC9B,IAAI;gBAAE,WAAW;YAAK;YAEtB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,MAAM,CAAC,EAAE;oBACpD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBAAE;wBAAU;oBAAS;gBAC5C;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,KAAK,OAAO,EAAE;oBAChB,IAAI;wBACF,MAAM,KAAK,IAAI;wBACf,OAAO,KAAK,KAAK;wBACjB,iBAAiB;wBACjB,WAAW;oBACb;oBACA,OAAO;wBAAE,SAAS;wBAAM,SAAS,KAAK,OAAO;oBAAC;gBAChD,OAAO;oBACL,IAAI;wBAAE,WAAW;oBAAM;oBACvB,OAAO;wBAAE,SAAS;wBAAO,SAAS,KAAK,OAAO;oBAAC;gBACjD;YACF,EAAE,OAAO,OAAO;gBACd,IAAI;oBAAE,WAAW;gBAAM;gBACvB,OAAO;oBAAE,SAAS;oBAAO,SAAS;gBAAe;YACnD;QACF;QAEA,KAAK;QACL,UAAU,OAAO,UAAkB,UAAkB,iBAAyB;YAC5E,IAAI;gBAAE,WAAW;YAAK;YAEtB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC,EAAE;oBACvD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB;wBACA;wBACA;wBACA;oBACF;gBACF;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,KAAK,OAAO,EAAE;oBAChB,IAAI;wBAAE,WAAW;oBAAM;oBACvB,OAAO;wBAAE,SAAS;wBAAM,SAAS,KAAK,OAAO;oBAAC;gBAChD,OAAO;oBACL,IAAI;wBAAE,WAAW;oBAAM;oBACvB,OAAO;wBAAE,SAAS;wBAAO,SAAS,KAAK,OAAO;oBAAC;gBACjD;YACF,EAAE,OAAO,OAAO;gBACd,IAAI;oBAAE,WAAW;gBAAM;gBACvB,OAAO;oBAAE,SAAS;oBAAO,SAAS;gBAAe;YACnD;QACF;QAEA,KAAK;QACL,QAAQ;YACN,IAAI;gBACF,MAAM;gBACN,OAAO;gBACP,iBAAiB;gBACjB,WAAW;YACb;QACF;QAEA,SAAS;QACT,WAAW;YACT,MAAM,EAAE,KAAK,EAAE,GAAG;YAElB,IAAI,CAAC,OAAO;gBACV;YACF;YAEA,IAAI;gBAAE,WAAW;YAAK;YAEtB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,QAAQ,CAAC,EAAE;oBACtD,QAAQ;oBACR,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;wBAClC,gBAAgB;oBAClB;gBACF;gBAEA,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,IAAI,KAAK,OAAO,EAAE;wBAChB,IAAI;4BACF,MAAM,KAAK,IAAI;4BACf,iBAAiB;4BACjB,WAAW;wBACb;oBACF,OAAO;wBACL,kBAAkB;wBAClB,MAAM,MAAM;oBACd;gBACF,OAAO;oBACL,kBAAkB;oBAClB,MAAM,MAAM;gBACd;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;gBAC3B,MAAM,MAAM;YACd;QACF;QAEA,SAAS;QACT,YAAY,CAAC;YACX,IAAI;gBAAE;YAAK;QACb;QAEA,SAAS;QACT,YAAY,CAAC;YACX,IAAI;gBAAE,WAAW;YAAQ;QAC3B;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY;IACZ,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,OAAO,MAAM,KAAK;YAClB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH;AAKG,MAAM,iBAAiB;IAC5B,MAAM,QAAQ,aAAa,QAAQ,GAAG,KAAK;IAC3C,OAAO,QAAQ;QAAE,eAAe,CAAC,OAAO,EAAE,OAAO;IAAC,IAAI,CAAC;AACzD", "debugId": null}}, {"offset": {"line": 338, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/mall/mall-client/src/components/Layout/Header.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport Link from 'next/link';\r\nimport { useRouter } from 'next/navigation';\r\nimport { MagnifyingGlassIcon, Bars3Icon, ShoppingCartIcon, UserIcon } from '@heroicons/react/24/outline';\r\nimport { useCartStore } from '../../store/cartStore';\r\nimport { useAuthStore } from '../../store/authStore';\r\n\r\nconst Header: React.FC = () => {\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\r\n  const router = useRouter();\r\n  const { totalItems } = useCartStore();\r\n  const { user, isAuthenticated, logout } = useAuthStore();\r\n\r\n  const handleSearch = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (searchQuery.trim()) {\r\n      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <header\r\n      className=\"bg-gray-800 text-white p-4 shadow-md\"\r\n      style={{\r\n        backgroundColor: '#1f2937',\r\n        color: 'white',\r\n        padding: '1rem',\r\n        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',\r\n        minHeight: '60px'\r\n      }}\r\n    >\r\n      <div className=\"container mx-auto\">\r\n        <div className=\"flex justify-between items-center\" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n          {/* Logo */}\r\n          <Link href=\"/\" className=\"text-2xl font-bold\">\r\n            E-commerce\r\n          </Link>\r\n\r\n          {/* 搜索框 - 桌面版 */}\r\n          <div className=\"hidden md:flex flex-1 max-w-md mx-8\">\r\n            <form onSubmit={handleSearch} className=\"w-full\">\r\n              <div className=\"relative\">\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"搜索商品...\"\r\n                  value={searchQuery}\r\n                  onChange={(e) => setSearchQuery(e.target.value)}\r\n                  className=\"w-full px-4 py-2 pl-10 text-gray-900 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                />\r\n                <MagnifyingGlassIcon className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\" />\r\n              </div>\r\n            </form>\r\n          </div>\r\n\r\n          {/* 導航菜單 - 桌面版 */}\r\n          <nav className=\"hidden md:flex items-center space-x-4\">\r\n            <Link href=\"/\" className=\"hover:text-gray-300\">首頁</Link>\r\n            <Link href=\"/products\" className=\"hover:text-gray-300\">商品</Link>\r\n            <Link href=\"/orders\" className=\"hover:text-gray-300\">訂單</Link>\r\n\r\n            {/* 購物車圖標 */}\r\n            <Link href=\"/cart\" className=\"relative hover:text-gray-300 flex items-center\">\r\n              <ShoppingCartIcon className=\"w-6 h-6\" />\r\n              {totalItems > 0 && (\r\n                <span className=\"absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\r\n                  {totalItems > 99 ? '99+' : totalItems}\r\n                </span>\r\n              )}\r\n            </Link>\r\n\r\n            {/* 用戶狀態 */}\r\n            {isAuthenticated ? (\r\n              <div className=\"relative group\">\r\n                <button className=\"flex items-center space-x-1 hover:text-gray-300\">\r\n                  <UserIcon className=\"w-5 h-5\" />\r\n                  <span>{user?.username}</span>\r\n                </button>\r\n\r\n                {/* 下拉菜單 */}\r\n                <div className=\"absolute right-0 mt-4 w-48 bg-white rounded-md shadow-lg py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200\">\r\n                  <Link href=\"/profile\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">\r\n                    個人中心\r\n                  </Link>\r\n                  <Link href=\"/cart\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">\r\n                    購物車\r\n                  </Link>\r\n                  <button\r\n                    onClick={() => {\r\n                      logout();\r\n                      router.push('/');\r\n                    }}\r\n                    className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\r\n                  >\r\n                    登出\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              <div className=\"flex items-center space-x-4\">\r\n                <Link href=\"/login\" className=\"hover:text-gray-300\">登錄</Link>\r\n                <Link href=\"/register\" className=\"bg-blue-600 text-white px-3 py-1 rounded-md hover:bg-blue-700\">\r\n                  註冊\r\n                </Link>\r\n              </div>\r\n            )}\r\n          </nav>\r\n\r\n          {/* 移動端菜單按鈕 */}\r\n          <div className=\"md:hidden\">\r\n            <button\r\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\r\n              className=\"text-white focus:outline-none\"\r\n            >\r\n              <Bars3Icon className=\"w-6 h-6\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 移動端菜單 */}\r\n        {isMobileMenuOpen && (\r\n          <div className=\"md:hidden mt-4 pb-4\">\r\n            {/* 移動端搜索框 */}\r\n            <form onSubmit={handleSearch} className=\"mb-4\">\r\n              <div className=\"relative\">\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"搜索商品...\"\r\n                  value={searchQuery}\r\n                  onChange={(e) => setSearchQuery(e.target.value)}\r\n                  className=\"w-full px-4 py-2 pl-10 text-gray-900 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                />\r\n                <MagnifyingGlassIcon className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\" />\r\n              </div>\r\n            </form>\r\n\r\n            {/* 移動端導航 */}\r\n            <nav className=\"flex flex-col space-y-2\">\r\n              <Link href=\"/\" className=\"block py-2 hover:text-gray-300\">首頁</Link>\r\n              <Link href=\"/products\" className=\"block py-2 hover:text-gray-300\">商品</Link>\r\n              <Link href=\"/orders\" className=\"block py-2 hover:text-gray-300\">訂單</Link>\r\n\r\n              {/* 移動端購物車鏈接 */}\r\n              <Link href=\"/cart\" className=\"flex items-center justify-between py-2 hover:text-gray-300\">\r\n                <span>購物車</span>\r\n                {totalItems > 0 && (\r\n                  <span className=\"bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\r\n                    {totalItems > 99 ? '99+' : totalItems}\r\n                  </span>\r\n                )}\r\n              </Link>\r\n\r\n              {/* 移動端用戶狀態 */}\r\n              {isAuthenticated ? (\r\n                <>\r\n                  <div className=\"border-t border-gray-600 pt-2 mt-2\">\r\n                    <div className=\"flex items-center space-x-2 py-2\">\r\n                      <UserIcon className=\"w-5 h-5\" />\r\n                      <span>歡迎，{user?.username}</span>\r\n                    </div>\r\n                  </div>\r\n                  <Link href=\"/profile\" className=\"block py-2 hover:text-gray-300\">個人中心</Link>\r\n                  <button\r\n                    onClick={() => {\r\n                      logout();\r\n                      router.push('/');\r\n                      setIsMobileMenuOpen(false);\r\n                    }}\r\n                    className=\"block w-full text-left py-2 hover:text-gray-300\"\r\n                  >\r\n                    登出\r\n                  </button>\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <div className=\"border-t border-gray-600 pt-2 mt-2\">\r\n                    <Link href=\"/login\" className=\"block py-2 hover:text-gray-300\">登錄</Link>\r\n                    <Link href=\"/register\" className=\"block py-2 hover:text-gray-300\">註冊</Link>\r\n                  </div>\r\n                </>\r\n              )}\r\n            </nav>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </header>\r\n  );\r\n};\r\n\r\nexport default Header;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAPA;;;;;;;;AASA,MAAM,SAAmB;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAClC,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAErD,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,YAAY,IAAI,IAAI;YACtB,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,mBAAmB,YAAY,IAAI,KAAK;QACnE;IACF;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,OAAO;YACL,iBAAiB;YACjB,OAAO;YACP,SAAS;YACT,WAAW;YACX,WAAW;QACb;kBAEA,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;oBAAoC,OAAO;wBAAE,SAAS;wBAAQ,gBAAgB;wBAAiB,YAAY;oBAAS;;sCAEjI,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAqB;;;;;;sCAK9C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,UAAU;gCAAc,WAAU;0CACtC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAU;;;;;;sDAEZ,8OAAC,qOAAA,CAAA,sBAAmB;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAMrC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAsB;;;;;;8CAC/C,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;8CAAsB;;;;;;8CACvD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAU,WAAU;8CAAsB;;;;;;8CAGrD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;;sDAC3B,8OAAC,+NAAA,CAAA,mBAAgB;4CAAC,WAAU;;;;;;wCAC3B,aAAa,mBACZ,8OAAC;4CAAK,WAAU;sDACb,aAAa,KAAK,QAAQ;;;;;;;;;;;;gCAMhC,gCACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAO,WAAU;;8DAChB,8OAAC,+MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;8DAAM,MAAM;;;;;;;;;;;;sDAIf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;8DAA0D;;;;;;8DAG1F,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,WAAU;8DAA0D;;;;;;8DAGvF,8OAAC;oDACC,SAAS;wDACP;wDACA,OAAO,IAAI,CAAC;oDACd;oDACA,WAAU;8DACX;;;;;;;;;;;;;;;;;yDAML,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAAsB;;;;;;sDACpD,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAY,WAAU;sDAAgE;;;;;;;;;;;;;;;;;;sCAQvG,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,oBAAoB,CAAC;gCACpC,WAAU;0CAEV,cAAA,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAM1B,kCACC,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAK,UAAU;4BAAc,WAAU;sCACtC,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;kDAEZ,8OAAC,qOAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKnC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAiC;;;;;;8CAC1D,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;8CAAiC;;;;;;8CAClE,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAU,WAAU;8CAAiC;;;;;;8CAGhE,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;;sDAC3B,8OAAC;sDAAK;;;;;;wCACL,aAAa,mBACZ,8OAAC;4CAAK,WAAU;sDACb,aAAa,KAAK,QAAQ;;;;;;;;;;;;gCAMhC,gCACC;;sDACE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,+MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;;4DAAK;4DAAI,MAAM;;;;;;;;;;;;;;;;;;sDAGpB,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAAiC;;;;;;sDACjE,8OAAC;4CACC,SAAS;gDACP;gDACA,OAAO,IAAI,CAAC;gDACZ,oBAAoB;4CACtB;4CACA,WAAU;sDACX;;;;;;;iEAKH;8CACE,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAiC;;;;;;0DAC/D,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUtF;uCAEe", "debugId": null}}]}