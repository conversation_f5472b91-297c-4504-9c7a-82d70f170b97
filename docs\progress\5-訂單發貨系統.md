# 5-訂單發貨系統

**方案編號**: 5  
**開始日期**: 2025-01-27  
**完成日期**: 進行中  
**狀態**: 🚧 進行中  
**負責人**: AI Assistant  
**上級方案**: [0-總方案](./0-總方案.md)  
**前置依賴**: [3-訂單系統](./3-訂單系統.md), [4-用戶認證系統](./4-用戶認證系統.md)

## 方案概述

開發完整的訂單發貨系統，實現用戶查看發貨進度和管理員發貨操作功能。包括發貨狀態管理、物流信息跟踪、發貨操作界面和發貨進度展示。

## 技術方案

### 後端架構
- **Spring Boot**: 發貨API服務
- **MyBatis**: 發貨數據持久化
- **MySQL**: 發貨信息存儲
- **JWT**: 權限控制（管理員發貨權限）

### 前端架構
- **Next.js**: 用戶端發貨進度查看
- **Ant Design Pro**: 管理端發貨操作界面
- **Zustand**: 發貨狀態管理
- **TypeScript**: 類型安全

## 實施計劃

### 階段1：後端發貨API開發
- [ ] 擴展訂單狀態管理
- [ ] 創建發貨信息實體和Mapper
- [ ] 開發發貨相關API接口
- [ ] 實現發貨權限控制

### 階段2：管理端發貨功能
- [ ] 訂單列表發貨操作
- [ ] 發貨信息錄入界面
- [ ] 批量發貨功能
- [ ] 發貨狀態更新

### 階段3：用戶端發貨進度
- [ ] 訂單詳情發貨信息展示
- [ ] 發貨進度時間軸
- [ ] 物流跟踪信息
- [ ] 發貨通知功能

### 階段4：系統集成測試
- [ ] 發貨流程端到端測試
- [ ] 權限控制測試
- [ ] 數據一致性測試
- [ ] 用戶體驗測試

## 實現詳情

### 1. 後端發貨API

#### 發貨信息實體
```java
public class ShippingInfo {
    private Long id;
    private Long orderId;
    private String trackingNumber;
    private String carrier;
    private String shippingMethod;
    private LocalDateTime shippedAt;
    private String notes;
}
```

#### 發貨API接口
- `POST /api/orders/{id}/ship` - 發貨操作
- `GET /api/orders/{id}/shipping` - 獲取發貨信息
- `PUT /api/orders/{id}/shipping` - 更新發貨信息
- `GET /api/admin/orders/pending-shipment` - 待發貨訂單列表

### 2. 管理端發貨功能

#### 訂單管理頁面增強
- 待發貨訂單篩選
- 發貨操作按鈕
- 批量發貨選擇
- 發貨信息錄入表單

#### 發貨操作流程
1. 選擇待發貨訂單
2. 錄入物流信息（快遞公司、運單號）
3. 確認發貨操作
4. 更新訂單狀態為"已發貨"
5. 發送發貨通知

### 3. 用戶端發貨進度

#### 訂單詳情頁面增強
- 發貨狀態展示
- 物流跟踪信息
- 發貨時間軸
- 預計送達時間

#### 發貨進度組件
```typescript
interface ShippingProgress {
  status: 'PENDING' | 'SHIPPED' | 'IN_TRANSIT' | 'DELIVERED';
  trackingNumber?: string;
  carrier?: string;
  shippedAt?: string;
  estimatedDelivery?: string;
  trackingEvents?: TrackingEvent[];
}
```

## 數據庫設計

### 發貨信息表 (shipping_info)
```sql
CREATE TABLE shipping_info (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_id BIGINT NOT NULL,
    tracking_number VARCHAR(100),
    carrier VARCHAR(50),
    shipping_method VARCHAR(50),
    shipped_at TIMESTAMP,
    estimated_delivery TIMESTAMP,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id)
);
```

### 物流跟踪表 (tracking_events)
```sql
CREATE TABLE tracking_events (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    shipping_id BIGINT NOT NULL,
    event_time TIMESTAMP NOT NULL,
    location VARCHAR(200),
    description TEXT NOT NULL,
    status VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (shipping_id) REFERENCES shipping_info(id)
);
```

## 業務邏輯

### 發貨狀態流轉
1. **PAID** (已付款) → **PROCESSING** (處理中)
2. **PROCESSING** (處理中) → **SHIPPED** (已發貨)
3. **SHIPPED** (已發貨) → **IN_TRANSIT** (運輸中)
4. **IN_TRANSIT** (運輸中) → **DELIVERED** (已送達)

### 發貨權限控制
- 只有ADMIN角色可以執行發貨操作
- 只能對已付款的訂單進行發貨
- 發貨後不能撤銷，只能更新物流信息

### 發貨通知機制
- 發貨後自動發送通知給用戶
- 提供物流跟踪號和查詢鏈接
- 預計送達時間提醒

## 文件結構

```
後端 (ecommerce-backend):
├── src/main/java/io/github/roshad/ecommerce/
│   ├── shipping/
│   │   ├── ShippingInfo.java
│   │   ├── TrackingEvent.java
│   │   ├── ShippingController.java
│   │   ├── ShippingService.java
│   │   ├── ShippingMapper.java
│   │   └── ShippingRequest.java
│   └── order/
│       └── OrderController.java (擴展發貨API)

前端用戶端 (mall-client):
├── src/
│   ├── types/
│   │   └── shipping.ts
│   ├── components/
│   │   └── Shipping/
│   │       ├── ShippingProgress.tsx
│   │       ├── TrackingTimeline.tsx
│   │       └── DeliveryInfo.tsx
│   └── app/orders/[id]/
│       └── page.tsx (增強發貨信息)

前端管理端 (mall-admin):
├── src/
│   ├── pages/
│   │   └── OrderManagement/
│   │       ├── OrderList.tsx (增強發貨功能)
│   │       ├── ShippingModal.tsx
│   │       └── BatchShipping.tsx
│   └── services/
│       └── shipping.ts
```

## 當前進度

### 已完成 ✅
- 基礎訂單系統
- 用戶認證系統
- 訂單狀態管理

### 進行中 🚧
- 後端發貨API開發
- 管理端發貨界面
- 用戶端發貨進度展示

### 待開始 ⏳
- 物流跟踪集成
- 發貨通知系統
- 批量發貨功能

---

**最後更新**: 2025-01-27  
**下次檢查**: 2025-01-28
